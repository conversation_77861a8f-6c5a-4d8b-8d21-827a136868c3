version: '3.8'

services:
  # Frontend - Production build served by Nginx
  frontend:
    image: my-app-frontend:${VERSION:-latest}
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - frontend-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Backend API - Production configuration
  backend:
    image: my-app-backend:${VERSION:-latest}
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
    environment:
      - NODE_ENV=production
      - PORT=8000
      - DATABASE_URL_FILE=/run/secrets/database_url
      - REDIS_URL=redis://cache:6379
      - JWT_SECRET_FILE=/run/secrets/jwt_secret
    secrets:
      - database_url
      - jwt_secret
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      - database
      - cache
    networks:
      - backend-network
      - frontend-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Database - PostgreSQL with backup
  database:
    image: postgres:13-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_DB_FILE=/run/secrets/postgres_db
      - POSTGRES_USER_FILE=/run/secrets/postgres_user
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
    secrets:
      - postgres_db
      - postgres_user
      - postgres_password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./backups:/backups
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$(cat /run/secrets/postgres_user)"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - backend-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Cache - Redis with persistence
  cache:
    image: redis:7-alpine
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass $$(cat /run/secrets/redis_password)
    secrets:
      - redis_password
    volumes:
      - redis-data:/data
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "$$(cat /run/secrets/redis_password)", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - backend-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Load Balancer - Nginx with SSL
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    depends_on:
      - frontend
      - backend
    networks:
      - frontend-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Database Backup Service
  backup:
    image: postgres:13-alpine
    restart: "no"
    environment:
      - PGPASSWORD_FILE=/run/secrets/postgres_password
    secrets:
      - postgres_password
      - postgres_user
      - postgres_db
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: /backup.sh
    depends_on:
      - database
    networks:
      - backend-network
    deploy:
      restart_policy:
        condition: none

  # Log Aggregation - Fluentd
  fluentd:
    image: fluent/fluentd:v1.14-debian-1
    restart: unless-stopped
    volumes:
      - ./fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - nginx-logs:/var/log/nginx:ro
    ports:
      - "24224:24224"
    networks:
      - monitoring-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    restart: unless-stopped
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.time=30d'
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD_FILE=/run/secrets/grafana_password
      - GF_USERS_ALLOW_SIGN_UP=false
    secrets:
      - grafana_password
    volumes:
      - grafana-data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# Production secrets
secrets:
  database_url:
    external: true
  jwt_secret:
    external: true
  postgres_db:
    external: true
  postgres_user:
    external: true
  postgres_password:
    external: true
  redis_password:
    external: true
  grafana_password:
    external: true

# Production volumes
volumes:
  postgres-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/myapp/data/postgres
  redis-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/myapp/data/redis
  prometheus-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/myapp/data/prometheus
  grafana-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/myapp/data/grafana
  nginx-logs:
    driver: local

# Production networks with custom subnets
networks:
  frontend-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
  backend-network:
    driver: bridge
    internal: true
    ipam:
      config:
        - subnet: **********/24
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/24
