# Docker Crash Course

A comprehensive guide to Docker containerization technology, from fundamentals to production deployment.

## Course Overview

This course takes you through Docker from the ground up, covering everything you need to know to use Docker effectively in your development workflow. You'll learn core concepts, practical commands, and real-world applications.

## What You'll Learn

- Docker fundamentals and architecture
- Container and image management
- Dockerfile creation and best practices
- Data persistence with volumes
- Container networking
- Multi-container applications with Docker Compose
- Production deployment strategies
- Troubleshooting and debugging techniques

## Prerequisites

- Basic command line knowledge
- Understanding of software development concepts (frontend, backend, API, database)
- Basic Git knowledge (clone, commit, push, pull)
- At least 3 months of programming experience

## Course Structure

### Core Topics

1. **[Docker Fundamentals](./01-docker-fundamentals.md)**
   - What is Docker and containerization
   - Virtual machines vs containers
   - Docker architecture and workflow

2. **[Installation and Setup](./02-installation-setup.md)**
   - Installing Docker on different platforms
   - Verifying installation
   - Running your first container

3. **[Images and Containers](./03-images-containers.md)**
   - Understanding Docker images
   - Container lifecycle management
   - Essential Docker commands

4. **[Dockerfile Creation](./04-dockerfile-creation.md)**
   - Dockerfile syntax and instructions
   - Building custom images
   - Image optimization techniques

5. **[Container Management](./05-container-management.md)**
   - Starting and stopping containers
   - Container interaction and debugging
   - Resource management

6. **[Volumes and Data Persistence](./06-volumes-data-persistence.md)**
   - Container filesystem concepts
   - Volume types and management
   - Data sharing strategies

7. **[Networking](./07-networking.md)**
   - Container networking fundamentals
   - Port mapping and exposure
   - Custom networks and communication

8. **[Docker Compose](./08-docker-compose.md)**
   - Multi-container applications
   - Service orchestration
   - Configuration management

9. **[Production Deployment](./09-production-deployment.md)**
   - Production best practices
   - Security considerations
   - Performance optimization

10. **[Troubleshooting and Debugging](./10-troubleshooting-debugging.md)**
    - Common issues and solutions
    - Debugging techniques
    - Log management

### Practical Examples

The `examples/` directory contains hands-on projects and code samples:

- **[Hello World](./examples/hello-world/)** - Simple Node.js application
- **[Web Application](./examples/web-application/)** - Full-stack app with frontend, backend, and database
- **[Python App](./examples/python-app/)** - Flask application with dependencies
- **[Multi-stage Build](./examples/multi-stage-build/)** - Optimized production builds
- **[Production Setup](./examples/production-setup/)** - Production-ready configuration

## How to Use This Course

1. **Read each topic sequentially** - Topics build upon previous concepts
2. **Practice with examples** - Run the commands and modify the code
3. **Take notes** - Write down key commands and concepts
4. **Experiment** - Try variations of the examples
5. **Build projects** - Apply concepts to your own applications

## Getting Started

1. Start with [Docker Fundamentals](./01-docker-fundamentals.md)
2. Follow the [Installation and Setup](./02-installation-setup.md) guide
3. Work through each topic in order
4. Practice with the provided examples

## Additional Resources

- [Docker Official Documentation](https://docs.docker.com/)
- [Docker Hub](https://hub.docker.com/)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)

---

*This course is designed to be practical and hands-on. By the end, you'll be able to containerize applications, manage Docker environments, and deploy containers in production.*
