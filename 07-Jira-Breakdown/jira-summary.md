jira is an advanced project management tool designed for agile teams to essentially optimize project planning execution and tracking the great thing about jira is they offer a free plan for up to 10 users which is ideal for agile and innovative small teams hey team welcome back to another video i hope everyone is having a great day now today we're going to launch into the project management tool called jira and cover how you can get started with the impressive free plan to manage your dynamic project workflows now if you're completely new to project management i recommend looking into trello as an entry-level project management tool to get started with before you jump into a more advanced software like jira both these tools offer exceptional free plans and stem from the same company called atlassian okay now just quickly before we go ahead and jump right into jarrah consider subscribing if you haven't done so already and that way you'll stay updated with actionable videos and tutorials designed to equip you with the skills knowledge and tools to grow your small business online and with that quick note out the way let's go ahead and jump straight into this jira tutorial okay team so first things first what we need to do is sign up to a free jira account to do this just head over to atlassian.com software slash jira and that's going to take you to this page what we want to do is get started for free and before we get started it's important to note that jira is firstly a software development tool for software teams however you can also create project management workflows that work for different types of businesses too not just for software development and in this beginner's tutorial we're going to show you an agile process as well as a traditional business workflow so with that said let's go ahead and get started for free now what i'm going to do is skip this initial process we've already created a fresh new account and what we're going to do is meet you on your dashboard once you've signed up so go ahead sign up for free and we'll meet you at your jared dashboard okay so we've gone ahead and created a fresh account and logged in to jira remember that jira as a project management software is first and foremost designed for software development and the first thing we're going to show you today is how to use jira to create a project management workflow for a software project so the whole idea of software development stems from creating an agile process an agile process can be broken down into the functionality the planning implementation testing and the review stage now once you move through this agile process and you get to the review stage you can either build your software or you can go back to the planning stage depending if you uncover any issues along that project workflow okay so what i'm going to do now is walk you through the process of creating a software development workflow and then within each step of this process i will explain the terminology surrounding each of these stages okay so diving right into our first project you can see that our first project is called call tracking software integration this is a software development program that we're going to create for a client so that is the name of our first project now you will see your own project name up here based on the information you gave when you initially signed up to jira however you can also navigate up to projects and click on create project and create a new project from scratch however we're going to stick to the cool tracking software integration which is our first project then what we want to do is navigate over to backlog this is where our software development journey begins essentially our project management workflow for a software development project okay so firstly what we need to do is create our first step in our project workflow all we need to do is navigate up to create and here you can see that we're about to create an issue now think about an issue as essentially a element within your project workflow that you want to create so essentially you're creating an element for your project workflow within jera that is called an issue so if we come down to issue type we can change the element to task bug or epic as well as story what we want to do first is select epic so our software development journey starts with an epic okay so epic is a new terminology that you need to know when it comes to software development epic is basically an abstract view of what we need to create for our clients what our clients want in this case it is a cool tracking software so we're going to come down to epic name and we're going to name this multi-feature tracking software because this is exactly what our client wants us to create for them so that our clients organization can start tracking customer phone calls and other phone tracking features so that is the epic multi-feature call tracking and then once you've added your epic name come down to summary and add a summary about your epic so for us we're going to add create a multi-feature call tracking software so once you've added a summary about your epic so remember that's the abstract view of what you want to create for your client or what your client needs and wants then below that we can add an attachment we can add further information about your epic we can also come down to reporter i'm going to leave that as myself as i created this new issue then below that we have an assignee i'm going to select an assignee later down the track we have priority labels and then below that we have sprint which is essentially the project management workflow and we're going to create that shortly so come down and click create and you can see that your epic has been created now what we need to do is navigate back up to create and then under our project that we have selected we now want to select story so if you click story this is the next step under epic this is essentially breaking down what is required to achieve your epic so for the purpose of this project call tracking software integration i want to come down to summary and add location call tracking which is the feature the requirement of the epic remember the epic that we created is cool tracking software multi-feature call tracking software and this is one of the stories or one of the features within my project that i need to complete in order to satisfy that epic i hope that makes sense so this in essence is the second stage to our project this is explaining what we need to do following this what we're going to do is create tasks that need to be completed in order to satisfy the stories and ultimately the epic and this project so again if we come down here we have the same options that you can go through i'm going to leave that as it is and come down and click create now i'm going to go ahead and create one more issue type which is going to be another story and this is going to be a different feature for the purpose of this tutorial the feature is going to be tracking keywords spoken by the client's customers so this is another story this is another feature that we need to create then again we have the same options down here we're gonna leave it as it is for now and come down and click create okay now as you can see we have two issue types two stories i can also navigate down here and create a story if i like i can select the option on the left hand side and select the issue type whether it's a story task or bug i'll exit out of that one i can also do the same by navigating up to create and then i can come down and select the issue type but for now we'll leave it as the two issues so i'll cancel out of that we have the two issues two stories now what we want to do is create subtasks within each of these issues these stories so to do that what we can do is click create issue navigate over to the left hand side and click task or we can navigate up to create and come down and select task and then you can fill out the information about the specific task but what we're going to do is exit out of that one again now if we head over to create sprint we can go ahead and create our project workflow so what i'm going to do is click on this issue and drag that up to my sprint and then my other issue over here my other story and drag that underneath this story so i now have two issues in my sprint for this particular project now i'm happy with those two stories what i'm going to do is navigate over to start sprint and then i can go ahead and name the sprint if i like but i'm happy with this name i can also change the duration of the sprint so i'm going to go ahead and go four weeks everything within this project workflow this sprint must be completed within four weeks and then i can add other information down here if i like but i'm going to go ahead and click start and here you can see your project sprint you can see that we've got our to do in progress and done sections we can also navigate up to complete sprint once all the tasks the stories have been completed now we have two stories over here with limited information what we can do is double click on each of these issues in this case these are stories and within stories we want to create specific tasks that our team can work towards to complete each of these stories and ultimately the sprint so firstly i can navigate over to a sign and i can assign an individual this is developer two they are in charge of this story below that we have reporter which is myself we have labels we can add and story points this shows how important this story is to complete so i'm going to go ahead and click four and then down here we have other options what i'm gonna do is navigate up to create subtask and create a subtask within this story create the tracking code for the call tracking this is the only subtask that we're going to have within this story once this subtask has been completed then this story will be completed so i'm happy with that story and sub task i'm going to click close and as you can see in my project workflow my sprint i have one story here one story here and then i have one task under this particular story location call tracking so i can go ahead and do the same for this issue and assign this story to developer 1 and then i'm going to go ahead and create a subtask for this story and that is going to be identify important keywords to track then i'm going to select create and that is all the information i want to add to this specific story i've got one subtask i can also add another one if i like but i'm going to leave that as it is and click close and as you can see i have two stories with one task under each of the stories once each task is under progress you can drag that across to progress again let's say for example this task over here was in progress and then it was completed say after a week then you could drag that over to done and you can see that all sub-tasks for parent issue cts-12 are now done that is our story do you want to update the parent issue which is the story to match what you would do is select update and as you can see this story this parent issue is now complete you can see the label done and that is because all our subtasks within this story have been completed okay so before we go ahead and finish this project workflow the software development project we need to identify what scrum roles are this is an important terminology that you need to understand when it comes to software development so scrum roles is essentially the hierarchy of employees working on software and how they interact and function to achieve an agile development firstly you have the project owner so this is the person that's responsible for the entire project this is the project management someone that deals with the customer understands what the customer or client wants to create then you have scrum master this is essentially the middle man that connects and manages the flow of information between the product owner so the manager of the project and the development team so for example the manager of this entire project so the manager of the epic would be the project owner then the scrum master would be say for example if we click on the sub task and then we navigate up to our story or parent issue over here on a sign this would be our scrum master because they're managing the activities of the developer team and they would act as the middleman that connects the flow of information between the product owner that owns this entire project or overseas this project and then the team that works on specific tasks so if i exit out of this one that leads us on to the scrum team and these are employees that are hands-on in completing project tasks so if i select this task here you can see that this task is unassigned what i would do is select a developer that's hands-on and complete specific tasks so i hope that makes sense you've got the product owner the scrum master and the scrum team so i'm going to exit out of this and that is essentially everything you need to know when it comes to scrum roles and managing your projects now you can also navigate up to people and you can invite new teammates you can also create specific teams and that's quite straightforward you can invite people via email then to the right of people we have apps essentially what you can do is further streamline the communication and integrations between other applications that you use within your organization you can connect these to jira and basically optimize that flow of information now if we navigate up to projects and we come down to create project we can actually go ahead and create other different types of projects for example a classic project next gen project if we select classic project and then down here to create a new project we can simply add a project name below that we have templates now at the moment we have scrum selected this is ideal for software development however if you want to create a project management workflow for another business type simply come down to change template here we can navigate up to software and come down to business and here you can see other project management templates that you can use for your business template for us we would just select the standard project management if we're creating a project management workflow for another business that is not a software-based business and then here i'm going to go ahead and quickly name this as website design project the key down here is going to be wdp below that we have the template which is project management selected again we can change the template if we like and then down here is what team is this for for us we're going to go ahead and keep design selected then come down and click create and just like that you now have a basic project management template project management board or workflow remember an issue is an element type you'd simply click create new issue and simply go ahead and add that new task in there however what we're going to do is leave it at that for today's beginners tutorial we've covered everything we wanted to cover when it comes to getting started with project management when it comes to agile teams and creating projects surrounding software development what we will do is create a more advanced tutorial in the near future that will help you dive deeper into what jira has to offer however you should now know how to get started with jira and decide if this is the best option for you or like we mentioned at the beginning of this tutorial getting started with trello which is a more user-friendly simplistic and entry-level project management software so take the time to experiment with all the different elements within jira to see if this is the right solution for you and your small business and there we have it guys that is it for today's overview of jira now if you have any questions about this tutorial make sure to pop them down below and with that said thank you so much for watching this tutorial all the way through to the end if you got value from this tutorial make sure you leave a comment a like subscribe and we will see you in the next video take care guys you